var LANGUAGE_OBJ=[{lang:"en",nm:"English"},{lang:"zh-cn",nm:"简体中文"},{lang:"zh",nm:"繁体中文"},{lang:"kr",nm:"한국어"}];function createLangTemplate(n){var e=`\n  <div style="z-index: 400;position: fixed;top: 0;right: 0;bottom: 0;left: 0;background-color: rgba(0, 0, 0, .8);">\n    <div class="language-box"\n      style="background: rgba(245,245,245,0.8);position: fixed;padding: 20px;border-radius: 15px;top: 50%;left: 50%;transform: translate(-50%, -50%);">\n      ${LANGUAGE_OBJ.map((e=>`\n        <div class="lang" style="padding: 10px 20px;border-radius: 5px;text-align: center;background: ${n==e.lang?"rgba(130,130,130,0.6)":"transparent"}" onClick="selectLang('${e.lang}')" onMouseOver="this.style.background='rgba(130,130,130,0.6)';" onMouseOut="this.style.background='transparent';">\n          ${e.nm}\n        </div>\n        `)).join("")}\n    </div>\n  </div>\n  `,a=document.createElement("div");a.innerHTML=e,a.id="langSelectBox",document.body.appendChild(a)}function closeLangTemplate(){var n=document.getElementById("langSelectBox");document.body.removeChild(n)}function selectLang(n){closeLangTemplate(),setLang(n)}
