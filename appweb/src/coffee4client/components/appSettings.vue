<template lang="pug">
div
  page-spinner(:loading="jumping")
  flash-message
  header.bar.bar-nav(style='background: transparent;')
    a.logout.pull-right(
      href='javascript:;',
      @click="clearDispVar(),goTo({url:'/1.5/user/logout'})",
      v-show="dispVar.userInfo.eml")
      | {{_('Logout')}}
  verify-third-party(:disp-var="dispVar")
  div.content
    div#rmPageBarColor(style="display:none") :name:mainTheme
    div.ad-link-wrapper(v-if="showAdLink")
      div
        | Please choose where to go
      div
        a(@click="goTo({url:'/1.5/cpm/analytics'})") Cpm Ads
      div
        a(@click="goTo({url:'/1.5/topagent/all'})") TopAgent
      div(v-if="dispVar.isProdSales")
        a(@click="goTo({url:'/1.5/crm/index'})") CRM Manage
      div(v-if="dispVar.isProdSales")
        a(@click="goTo({url:'/1.5/listingagent/index'})") Listing Agent Manage
      div.btn.btn-primary.btn-block(@click="showAdLink = false;" style="margin-top: 50px;") Close
    div.isLoggedIn(v-if="dispVar.userInfo.eml")
      div.userInfo
        //- /1.5/settings/editUserProfileImg
        div.avt
          img(src="/img/logo.png", :src="dispVar.userInfo.avt || '/img/logo.png'")
        div.info
          div.name {{dispVar.userInfo.fnm || dispVar.userInfo.nm}}
          //- div.sgn {{dispVar.userInfo.sgn}}  v-if="r.k[0] !== '_'"
          div.role-wrapper
            span.role {{dispVar.userInfo.roles.r.v}}
            span.role(v-for="r in dispVar.userInfo.roles.l", :class="{vip:isVip(r.k)}")
              //- span.fa.fa-vip(v-if="r.k=='vip'")
              //- img(src="/img/realtor.png", :src="'/img/' + r.k + '.png'")
              | {{_(r.v)}}
      div.links
        div.msg(v-if="!dispVar.isChatBlocked",:class="{'column-4':dispVar.isAdmin || dispVar.isVipUser}",@click="goTo({url:'/chat/list'})")
          div
            i.fa.fa-chat(:class="{'new':dispVar.hasNewMsg}")
          div
            | {{_('Chat','settings')}}
        div.fav.column-4(v-show="dispVar.isAdmin || dispVar.isVipUser", @click="goTo({url:'/1.5/form/forminput?d=/1.5/settings'})")
          div
            i.icon.sprite16-18.sprite16-7-5(style="position:relative")
              div.new-form-input(v-if="dispVar.newFormInput")
                | {{dispVar.newFormInput}}
          div
            | {{_('Register','settings')}}
        div.fav(:class="{'column-4':dispVar.isAdmin || dispVar.isVipUser}",@click="goTo({url:'/1.5/saves/properties?d=/1.5/settings&grp=0'})")
          div
            i.fa.fa-heart-o
          div
            | {{_('Saved','settings')}}

        div.mylisting(:class="{'column-4':dispVar.isAdmin || dispVar.isVipUser}",@click="goTo({url:'/1.5/promote/mylisting?d=/1.5/settings'})")
          div
            i.icon.sprite16-18.sprite16-7-6
          div
            | {{_('My Listings')}}
        //- div.vip(@click="learnMore()")
        //-   div
        //-     i.fa.fa-vip
        //-     //(v-show="isRealtor")
        //-     //- img(src="/img/v_visitor.png", :src="'/img/v_'+dispVar.userInfo.roles.r.k+'.png'", v-show="!isRealtor")
        //-   div
        //-     | {{dispVar.userInfo.roles.r.v}}
    div.login(v-else)
      a.login-btn.btn.block(href="/1.5/user/login") {{_('Login Or Sign up')}}
    ul.table-view
      li.table-view-divider
      li.langSelect.table-view-cell.media(style="display:flex;justify-content:space-between;align-items:flex-start;")
        span.alignStretch
          <svg viewBox="0 0 100 100" width="16" height="16" style="color: #333;">
            <use :xlink:href="langIco"></use>
          </svg>
          span.media-body {{_('Language')}}
            span(v-if="dispVar.lang != 'en'",style="padding-left: 5px;") Language
        span.btn-group.pull-right
          a.btn.btn-default(href='javascript:void 0',v-for='lang in dispVar.languageAbbrObj',@click="createLangTemplate(dispVar.lang)", v-show="isActive(lang.k)") {{lang.v}}
      li.langCover.table-view-cell.media
        a(style="display:flex;justify-content:space-between;",href='javascript:void 0')
          span.alignStretch
            <svg viewBox="0 0 105 100" width="16" height="16" style="color: #333;">
              <use :xlink:href="setupIco"></use>
            </svg>
            span.media-body {{_('App Setup')}}
          span.pull-right(@click="setupApp()",style="color: rgb(66, 139, 202);")
            span {{_('Setup')}}
      //- li.langCover.table-view-cell.media(v-if="dispVar.isLoggedIn")
      //-   a(href='javascript:void 0',@click="goTo({url:'/1.5/settings/user'})")
      //-     span.media-object.pull-left.fa.fa-gear(style="font-size: 18px;font-weight: bold;")
      //-     span.media-body {{_('App Settings')}}
      li.langCover.table-view-cell.media(v-if="dispVar.isLoggedIn")
        a(herf="javascript:void 0",@click="goTo({url:'/1.5/settings/notification'})")
          span.alignStretch
            <svg viewBox="0 0 100 100" width="16" height="16" style="color: #333;">
              <use :xlink:href="notifiIco"></use>
            </svg>
            span.media-body {{_('Notifications')}}

      //- li.langSelect.table-view-cell.media
      //-   span.media-object.pull-left.fa.fa-language(style="font-size: 18px;font-weight: bold;")
      //-   span.media-body {{_('Readable Language')}}
      //-   span.btn-group.pull-right
      //-     a.btn.btn-default(href='javascript:void 0', @click="setRLang('en')", :class="isRActive('en')") En
      //-     a.btn.btn-default(href='javascript:void 0', @click="setRLang('zh-cn')", :class="isRActive('zh-cn')") 简
      //-     a.btn.btn-default(href='javascript:void 0', @click="setRLang('zh')", :class="isRActive('zh')") 繁
      li.table-view-divider
      li.media(:class="{'table-view-cell':!l.spl, 'table-view-divider':l.spl, 'noBotBorder':l.nbb}", v-for="l in uls", v-show="dispVar[l.show]")
        a(href='javascript:void 0',style="display:flex;justify-content:space-between;" v-if="!l.spl", @click="clickedLi(l)", :href="l.actionHref")
          span.alignStretch
            <svg viewBox="0 0 100 100" width="16" height="16" style="color: #333;">
              <use :xlink:href="l.ico"></use>
            </svg>
            span.media-body(:class="{'inline':l.txt}") {{_(l.t,l.ctx)}}
          //- span.pull-right.new-form-input(v-if="l.t=='WeForm'&&dispVar.newFormInput")
          //-   | {{dispVar.newFormInput}}
          span.textEllipsis.pull-right(v-show="l.txt", :style="l.txtStyle", :class="{new:dispVar[l.new]}")
            span(v-show='l.txt == "stringHelpCenter"') {{_('Quick Start')}}
            span(v-show='l.txt !== "stringHelpCenter"') {{dispVar[l.txt]}}
//- div.help
//-   //- div.wechat
//-   //-   | WeChat: RealMasterCanada
//-   //- div.tel
//-   //-   a(href="tel:**********") ************
//-   //- div.mail
//-   //-   a(href="mailto:"dispVar.defaultEmail) {{dispVar.defaultEmail}}
//-   div.claim
//-     p
//-       | RealMaster does not guarentee the accuracy of information available for listings and schools.  Double-checking with Real Estate Board and District School Board is strongly recommended.
//-   div.cp-right
//-     i.fa.fa-copyright
//-     | RealMaster Technology Inc. All Rights Reserved
  //- # li class: 'table-view-cell media', ->
  //- #   a class: '', href: 'javascript:connect58();', dataIgnore: 'push', ->
  //- #     span class: 'media-object pull-left icon fa fa-user-secret fa-fw'
  //- #     div class: 'media-body',-> _ 'Connect 58.com Account'
  //- # http://openapi.58.com/oauth2/authorize?client_id=**************&response_type=code&redirect_uri=http://realmaster.com/callback/58
  //- #!!! dont remove
</template>

<script>
import PageSpinner from './frac/PageSpinner.vue'
import VerifyThirdParty from './frac/VerifyThirdParty.vue'
import pagedata_mixins from './pagedata_mixins'
import FlashMessage from './frac/FlashMessage.vue'

export default {
  mixins: [pagedata_mixins],
  computed:{
    isRealtor:function () {
      var r;
      if ((r = this.dispVar.userInfo.roles) && r.r && r.r.k == 'realtor') {
        return true;
      }
      return false;
    }
  },
  data () {
    return {
      showAdLink:false,
      dispVar:{
        defaultEmail:'',
        coreVer:   '2.7.0',
        appVer:    '4.0.0a',
        always:    true,
        isLoggedIn: false,
        lang:      'En',
        rlang:     [],
        isApp:     false,
        hasNewMsg: false,
        newFormInput: 0,
        hasWechat: false,
        userInfo:  {
          roles:{r:{},l:[]}
        },
        formOrganizer: false,
        weChatAccount: 'RealMasterCanada',
        telNumber: '************',
        email: '<EMAIL>',
        forumAdmin: false,
        isProdSales:false,
        userCity:{},
        languageAbbrObj:[],
        isShowMyAD: false,
        showCrditRprt: false,
        isRealGroup: false,
        isWaitingVerifyRealtor: false,
        isRealtor: false,
      },
      jsVer:'',
      datas:[
        'defaultEmail',
        'isCip',
        'isApp',
        'userInfo',
        'hasNewMsg',
        'showForum',
        'coreVer',
        'appVer',
        'isLoggedIn',
        'lang',
        'rlang',
        'hasNewVer',
        'verifiedRole',
        'formOrganizer',
        'forumAdmin',
        'isProdSales',
        'newFormInput',
        'userCity',
        'isVipUser',
        'isAdmin',
        'isChatBlocked',
        // 'realmasterHelpUrl',
        'languageAbbrObj',
        'appmode',
        'isWaitingVerifyRealtor',
        'isRealGroup',
        'isRealtor'
      ],
      actPos:  50,
      jumping: false,
      reqUrl:'',
      uls: [
        {t:'Edit Profile',
          ctx:'personal',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-edit',
          url:'/1.5/settings/editProfile?d=/1.5/settings',
          show:'isLoggedIn'
        },
        // {t:'Edit Other Profile',
        //   ctx:'personal',
        //   ico:'fa-edit',
        //   url:'/1.5/settings/editOtherProfile?d=/1.5/settings',
        //   show:'forumAdmin'
        // },
        {t:'Change Password',
          ctx:'',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-password',
          url:'/1.5/user/changePwd?d=/1.5/settings',
          show:'isLoggedIn'
        },
        {t:'Verification',
          ctx:'',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-verification',
          url:'/1.5/user/verify?tp=merchant',
          show:'isLoggedIn',
          verify:true,
        },
        {t:'Connect Wechat Account',
          ctx:'',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-wechat',
          url:'',
          auth:true,
          show:'hasWechat'
        },
        // {t:'My Group',
        //   ctx:'',
        //   ico:'fa-group',
        //   url:'/group',
        //   nbb:1,
        //   show:'isLoggedIn'
        // },
        // {spl:1, show:'isLoggedIn'},

        // {t:'Email Subscription',
        //   ctx:'settings',
        //   ico:'fa-envelope-o',
        //   url:'/1.5/settings/subscription',
        //   show:'isLoggedIn'
        // },
        // {t:'My Properties',
        //   ctx:'settings',
        //   ico:'fa-dollar-flower',
        //   url:'/1.5/settings/myProperty',
        //   show:'isLoggedIn',
        //   nbb:1
        // },
        {spl:1, show:'isLoggedIn'},
        // {t:'WeForm manage',
        //   ctx:'settings',
        //   ico:'fa-edit',
        //   url:'/1.5/form',
        //   show:'formOrganizer'
        // },
        // {t:'WeForm',
        //   ctx:'settings',
        //   ico:'fa-edit',
        //   url:'/1.5/form/forminput?d=/1.5/settings',
        //   show:'isVipUser',
        //   show1: 'isAdmin'
        // },
        // {
        //   t:'Favs',
        //     ctx:'settings',
        //     ico:'fa-heart-o',
        //     url:'/1.5/mapSearch?d=/1.5/settings&grp=0',
        //     show:'isLoggedIn'
        // },
        {t:'Token',
          ctx:'token',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-token',
          url:'/token/history?type=own',
          show:'isShowToken',
          txt:'tokenRecord',
        },
        {t:'Credit Report',
          ctx:'creditReport',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-report',
          url:'/equifax/history',
          show:'showCrditRprt'
        },
        {t:'WeSite',
          ctx:'wesite',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-wesite',
          url:'/1.5/wesite?d=/1.5/settings',
          show:'isLoggedIn'
        },
        {t:'WePage',
          ctx:'',
          ss:{
            fontSize: '19px',
            marginLeft: '-1px'
          },
          ico:'/img/sprite/sprite-0.0.1.svg#ss-wepage',
          url:'/1.5/wecard?d=/1.5/settings',
          show:'isLoggedIn'
        },
        {
          t:'WeAD',
          ctx:'cpm',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-myad',
          url:'/1.5/cpm/analytics?d=/1.5/settings',
          show:'isShowMyAD'
        },
        // {t:'Tools',
        //   ctx:'settings',
        //   ico:'fa-calculator',
        //   url:'/1.5/tools',
        //   show:'isLoggedIn',
        //   nbb:1
        // },
        // {spl:1},
        {spl:1, show:'isLoggedIn'},
        {t:'Share to friends',
          ctx:'share',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-share',
          url:'/app-download?action=showSMB',
          show:'always',
        },
        // ********@fred@allen 不显示help
        // {t:'Help',
        //   ctx:'',
        //   ico:'fa-question-circle',
        //   url:'',
        //   txt:'stringHelpCenter',
        //   txtStyle:{
        //     color:'rgb(66, 139, 202)'
        //   },
        //   inAppB:'realmasterHelpUrl',
        //   show:'always'
        // },
        // ********@allen 不显示wechat
        // {t:'WeChat',
        //   ctx:'',
        //   ico:'fa-wechat',
        //   url:'',
        //   txt:'weChatAccount',
        //   show:'always'
        // },
        {t:'Tel',
          ctx:'',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-tel',
          url:'',
          txt:'telNumber',
          txtStyle:{
            color:'rgb(66, 139, 202)'
          },
          actionHref:'tel:**********',
          show:'always'
        },
        {t:'Email',
          ctx:'',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-email',
          url:'',
          txt:'email',
          txtStyle:{
            color:'rgb(66, 139, 202)'
          },
          // 使用动态获取的defaultemail会报错
          actionHref:'mailto:<EMAIL>',
          show:'always'
        },
        {t:'Feedback',
          ctx:'settings',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-edit',
          url:'',
          txtStyle:{
            color:'rgb(66, 139, 202)'
          },
          inTB:'/1.5/form/feedback',
          show:'always'
        },
        {t:'Version',
          ctx:'',
          ico:'/img/sprite/sprite-0.0.1.svg#ss-version',
          url:'',
          txt:'appVer',
          show:'always'
        },
        // {t:'Webversion',
        //   ctx:'',
        //   ico:'fa-info-circle',
        //   url:'',
        //   txt:'WEB_VER',
        //   show:'isAdmin'
        // },
        {t:'Core',
          ctx:'app version',
          ico:'',
          url:'/getapp',
          ss:{
            width: '14px',
            height: '0px'
          },
          txt:'coreVer',
          txtStyle:{
            color:'rgb(66, 139, 202)'
          },
          show:'always',
          nbb:1,
          new:'hasNewVer'
        }
      ],
      lang:'en',
      langIco: '/img/sprite/sprite-0.0.1.svg#ss-language',
      setupIco: '/img/sprite/sprite-0.0.1.svg#ss-setup',
      notifiIco: '/img/sprite/sprite-0.0.1.svg#ss-notification',
    };
  },
  components: {
    PageSpinner,
    VerifyThirdParty,
    FlashMessage
  },
  beforeMount () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    this.getPageData(this.datas, {}, true);
  },
  mounted () {
    var self = this, bus = window.bus;
    if (this.dispVar.defaultEmail){
      this.dispVar.email = this.dispVar.defaultEmail;
    }
    // if(RMSrv && RMSrv.setBarColor){
      // change color back to mainTheme, red/green
      // RMSrv.setBarColor({name:'mainTheme'})
    // }
    //after getPageData
    if ('function' == typeof RMSrv.action) {
      RMSrv.onReady(()=>{
        // var location = window.location.href;
        // var domain = self.extractDomain(location);
        // RMSrv.setItemObj('Cookie@'+domain,document.cookie);
        RMSrv.action('ver', function(ver){
          self.jsVer = ver;
          let curVer = self.dispVar.appVer;
          if (!/-/.test(curVer)) {
            self.dispVar = Object.assign(self.dispVar, {appVer:curVer+'-'+ver})
          }
        });
        // RMSrv.action({tp:'alert',msg:'test msg'});
        // window.postMessage(JSON.stringify({tp:'alert',msg:'test msg'}));
      })
    }
    bus.$on('pagedata-retrieved', function (d) {
      if (d.appVer && self.jsVer) {
        d.appVer = d.appVer+'-'+self.jsVer;
      }
      self.dispVar = Object.assign(self.dispVar, d);
      if (self.dispVar.isApp && (typeof RMSrv !== 'undefined') && (typeof RMSrv.hasWechat == 'function')) {
        RMSrv.hasWechat(function(has) {
          if (has) {
            self.dispVar.hasWechat = self.dispVar.isLoggedIn;
          }
        });
      }
      if(self.dispVar.isAdmin || self.dispVar.isVipUser){
        self.$set(self.dispVar, 'isShowMyAD', true);
      }
      if(self.dispVar.isAdmin || self.dispVar.isRealGroup){
        self.dispVar.showCrditRprt = true;
      }
      if(self.dispVar.isApp && self.dispVar.isLoggedIn){
        self.checkTokenRecord();
      }
    });
    // createLangTemplate 函数已经在 methods 中定义，无需外部依赖
    window.setLang = this.setLang;
  },
  events: {},
  methods: {
    /**
     * 创建语言选择模板
     * @param {string} currentLang - 当前选中的语言
     */
    createLangTemplate(currentLang) {
      const LANGUAGE_OBJ = [
        { lang: 'en', nm: 'English' },
        { lang: 'zh-cn', nm: '简体中文' },
        { lang: 'zh', nm: '繁体中文' },
        { lang: 'kr', nm: '한국어' }
      ];

      const template = `
        <div style="z-index: 400;position: fixed;top: 0;right: 0;bottom: 0;left: 0;background-color: rgba(0, 0, 0, .8);">
          <div class="language-box" style="background: rgba(245,245,245,0.8);position: fixed;padding: 20px;border-radius: 15px;top: 50%;left: 50%;transform: translate(-50%, -50%);">
            ${LANGUAGE_OBJ.map(item => `
              <div class="lang" style="padding: 10px 20px;border-radius: 5px;text-align: center;background: ${currentLang === item.lang ? 'rgba(130,130,130,0.6)' : 'transparent'}" onClick="selectLang('${item.lang}')" onMouseOver="this.style.background='rgba(130,130,130,0.6)';" onMouseOut="this.style.background='transparent';">
                ${item.nm}
              </div>
            `).join('')}
          </div>
        </div>
      `;

      const div = document.createElement('div');
      div.innerHTML = template;
      div.id = 'langSelectBox';
      document.body.appendChild(div);

      // 定义全局函数供模板中的 onClick 事件使用
      if (!window.closeLangTemplate) {
        window.closeLangTemplate = () => {
          const langSelectBox = document.getElementById('langSelectBox');
          if (langSelectBox) {
            document.body.removeChild(langSelectBox);
          }
        };
      }

      if (!window.selectLang) {
        window.selectLang = (lang) => {
          window.closeLangTemplate();
          this.setLang(lang);
        };
      }
    },

    // showMenu(show) {
    //   var show = show.split(',');
    //   for (let key of show) {
    //     if (this.dispVar[key])
    //       return true;
    //   }
    // },
    openTbrowser(url) {
      RMSrv.openTBrowser(url, {nojump:true, title: this._('RealMaster')});
    },
    isVip(k){
      return ['vip','vip_plus','vip_client','vip_alliance'].indexOf(k)>=0;
    },
    setupApp(){
      window.location = '/1.5/index?src=setting';
    },
    clearDispVar(){
      delete localStorage.dispVar;
      // 点击退出登录删掉保存的用户邮箱
      RMSrv.removeItemObj('recordEml');
    },
    isActive (lang) {
      // check user.locale, not dispvar.lang
      return this.dispVar.lang === lang ? 'active' : '';
    },
    isRActive(lang) {
      return this.dispVar.rlang.indexOf(lang) < 0 ? 'active':'';
    },
    setLang (lang) {
      if (lang) {
        // NOTE: sync lang to native in index already
        // RMSrv.setAppLang(lang);
        this.jumping = true;
        delete window.localStorage.translateCache;
        RMSrv.removeItemObj('l10n.localCache');
        // window.localStorage.lang = lang;
        window.location = "/1.5/settings/lang?l=" + lang;
      }
    },
    // setRLang (l){
    //   var idx = this.dispVar.rlang.indexOf(l);
    //   if (idx < 0) {
    //     this.dispVar.rlang.push(l);
    //   } else {
    //     this.dispVar.rlang.splice(idx,1);
    //   }
    //   var self = this;
    //   self.$http.post('/1.5/settings/rlang', {rlang:this.dispVar.rlang}).then(
    //     function (ret) {
    //       ret = ret.data;
    //       if (ret.ok) {
    //         console.log(ret);
    //       } else {
    //         console.error(ret);
    //       }
    //     },
    //     function (ret) {
    //       // console.error( "server-error" );
    //       RMSrv.dialogAlert( "server-error" );
    //     }
    //   );
    // },
    wechatAuth () {
      RMSrv.wechatAuth((err)=>{
        if(err){
          this.jumping = false;
        }
      });
      this.jumping = true;
      return false;
    },
    learnMore (tgt) {
      // var url;
      // url = '/event?tpl=getvip';
      // if (this.dispVar.lang) {
      //   url += "&lang=" + this.dispVar.lang;
      // }
      // if (tgt) {
      //   url = tgt;
      // }
      // return RMSrv.showInBrowser(url);
      return window.bus.$emit('3p-show-verify');
    },
    goTo (d) {
      var url = d.url;
      var self = this;
      if (url) {
        this.jumping = true;
        if (this.isNewerVer(self.dispVar.coreVer, '5.7.1') && /mapSearch/.test(url)) {
          // alert('here')
          // setTimeout(function () {
          self.jumping=false;
          // }, 90);
        }
        setTimeout(function () {
          window.location = url;
        }, 10);
      }
    },
    clickedLi (li) {
      if (li.verify) {
        return window.bus.$emit('3p-show-verify');
      } else if (li.url) {
        if (li.actionHref) {
          return true;
        } else {
          this.goTo({url:li.url});
        }
      } else if (li.inAppB) {
        var inAppB = this.dispVar[li.inAppB];
        RMSrv.showInBrowser(inAppB);
      } else if (li.inTB) {
        RMSrv.openTBrowser(li.inTB);
      } else if (li.auth){
        // clicked wechat
        this.wechatAuth();
      }
      if(li.t == 'Showing'){
        trackEventOnGoogle('settings', 'openShowing')
      }
    },
    checkTokenRecord(){
      let self = this;
      self.$http.post('/token/getTokenBalance', {isHistory:false}).then(
        function (ret) {
          let data = ret.data;
          if (data.ok && data.result.isShow) {
            self.$set(self.dispVar, 'isShowToken', true);
            if(!data.result.hasRecord){
              self.$set(self.dispVar, 'tokenRecord', '');
              return
            }
            let tokens = [];
            data.result.tokens.forEach((ele)=>{
              if(ele.tp.length){
                tokens.push(`${self._(ele.tp,'token')}:$${ele.token}`);
              }else{ // tp为''，表示token类型为默认值，显示在最前面
                tokens.unshift(ele.token);
              }
            });
            self.$set(self.dispVar, 'tokenRecord', tokens.join(', '));
          } else {
            self.$set(self.dispVar, 'isShowToken', false);
            self.$set(self.dispVar, 'tokenRecord', '');
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    }
  }
}


</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
@import '../../style/sass/_base.scss';
@import "/css/sprite.min.css";
.ad-link-wrapper {
  position: absolute;
  z-index: 10;
  width: 100%;
  height: 100%;
  padding: 50px;
}
.ad-link-wrapper div {
  padding: 15px;
}
.bar.bar-nav .logout {
  position: relative;
  z-index: 20;
  padding-top: 10px;
  padding-bottom: 10px;
  font-size: 16px;
  // color: white;
  padding-right: 10px;
}

.bar .btn-link {
  color: white;
  outline: 0;
}

.content {
  background: #fafafa;
  padding-bottom: 51px;
}
::-webkit-scrollbar{
  display: none;
}
.content > * {
  background-color: white;
}

.help {
  text-align: center;
  /* border-top: 0.5px solid  #e8e8e8; */
  /* border-bottom: 0.5px solid  #e8e8e8; */
  padding: 5px 3px;
  margin-bottom: 10px;
  background: #fafafa;
}

.help > div {
  padding: 2px 0;
}

.help .cp-right {
  color: #666;
  font-size: 14px;
}

.help .claim p {
  font-size: 10px;
  color: #ADACAC;
  line-height: 1.3em;
  margin-bottom: 0;
}

.table-view-cell {
  padding-right: 40px;
  border-bottom: 0.5px solid  #f1f1f1;
}

.table-view-divider {
  border-top: 1px none;
  border-bottom: 1px none;
  background-color: #f1f1f1;
}

.table-view-cell .media-object {
  color: #666;
  min-width: 20px;
  font-size: 17px;
  padding-top: 1px;
}

.media-object.bold {
  font-weight: bold;
  margin-left: -1px;
  margin-right: 11px;
}

.table-view-cell > a {
  color: #666;
}

.isLoggedIn .links > div, .userInfo > div {
  display: inline-block;
}

.isLoggedIn .links > div {
  width: 25%;
  vertical-align: top;
  height: 100%;
}
.isLoggedIn .links .column-4 {
  width: 25%!important;
  color: #333;
}
.userInfo {
  background: #7f7f7f;
  padding: 20px 0px 18px 16px;
  .fa-vip {
    color: #e03131;
    margin: 1px 5px 1px 0px;
    /* background: white; */
    border-radius: 50%;
  }
  .name {
    font-size: 18px;
  }
  .info {
    width: calc(100% - 72px);
    height: 72px;
    vertical-align: top;
    padding-left: 15px;
    padding-top: 12px;
    color: white;
  }
  .avt {
    width: 72px;
    height: 72px;
    img {
      width: 100%;
      height: auto;
      border-radius: 50%;
    }
  }
}

.links {
  text-align: center;
  padding: 14px 0 10px 0;
  border-top: 0.5px solid  #f1f1f1;
  font-size: 12px;
}

.links i.fa {
  color: #f25a60;
  font-size: 19px;
}

.links > div:first-child {
  border-left: none;
}

.links > div {
  /* border-left: 0.5px solid  #f1f1f1; */
}

.msg, .fav, .vip, .mylisting, .ads {
  color: #666;
}

.fav, .vip img {
  width: 19px;
  height: 19px;
}

div.login {
  height: 90px;
}

.login-btn {
  border-radius: 2px;
  border: 1px solid #666;
  color: #666;
  font-size: 16px;
  text-align: center;
  padding: 12px 15px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.langSelect {
  padding: 7px 15px 7px 15px;
}

.langSelect .media-object {
  padding-top: 4px;
}

.langSelect .media-body {
  padding-top: 3px;
  vertical-align: top;
  color: #666;
  font-size: 17px;
  display: inline-block;
}

.table-view-cell .media-body {
  font-size: 15px;
  font-weight: bold;
  color: #333;
}

.table-view-cell a .pull-right {
  font-size: 12px;
}

.table-view-cell .media-body.inline {
  display: inline;
}

.table-view-cell.noBotBorder {
  border: none;
}

.info .role img {
  height: 15px;
  width: 15px;
  vertical-align: top;
  margin-top: 3px;
  margin-right: 3px;
}

.info .role {
  font-size: 13px;
  color: #d8d8d8;
  margin-right: 10px;
  vertical-align: top;
}

.info .role-wrapper {
  margin-top: 5px;
}

.info .role.vip {
  background: #e03131;
  color: #f1f1f1;
  border-radius: 3px;
  padding: 1px 4px 2px;
  font-size: 11px;
}

.btn-group .btn {
  border-radius: 0;
}

.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group .btn + .btn, .btn-group .btn + .btn-group, .btn-group .btn-group + .btn, .btn-group .btn-group + .btn-group {
  margin-left: -1px;
}

.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0;
}

.btn-group > .btn:last-child:not(:first-child), .btn-group > .dropdown-toggle:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group > .btn:first-child {
  margin-left: 0;
}

.btn-group-vertical > .btn, .btn-group > .btn {
  position: relative;
  float: left;
}

.msg .new.fa:after {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #e03131;
  z-index: 10;
  position: relative;
  display: inline-block;
  right: -5px;
  top: -10px;
}

li.media .new:before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #e03131;
  z-index: 10;
  position: relative;
  right: -45px;
  top: -12px;
  display: inline-block;
}
.new-form-input {
  font-size: 8px;
  border: none;
  color: white !important;
  text-align: center;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e03131;
  /* display: block; */
  position: absolute;
  top: 0px;
  z-index: 10;
  right: -15px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.textEllipsis{
  width: 70%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: right;
}
svg{
  margin-right: 10px;
}
.alignStretch{
  display:flex;
  align-items:center;
}
</style>
